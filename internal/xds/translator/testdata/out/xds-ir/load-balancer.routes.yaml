- ignorePortInHostMatching: true
  name: first-listener
  virtualHosts:
  - domains:
    - '*'
    name: first-listener/*
    routes:
    - match:
        prefix: /
      name: first-route
      route:
        cluster: first-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: second-route
      route:
        cluster: second-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: third-route
      route:
        cluster: third-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: fourth-route
      route:
        cluster: fourth-route-dest
        hashPolicy:
        - connectionProperties:
            sourceIp: true
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: fifth-route
      route:
        cluster: fifth-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: sixth-route
      route:
        cluster: sixth-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: seventh-route
      route:
        cluster: seventh-route-dest
        hashPolicy:
        - header:
            headerName: name
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: eighth-route
      route:
        cluster: eighth-route-dest
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: ninth-route
      route:
        cluster: ninth-route-dest
        hashPolicy:
        - cookie:
            name: test
        upgradeConfigs:
        - upgradeType: websocket
    - match:
        prefix: /
      name: tenth-route
      route:
        cluster: tenth-route-dest
        hashPolicy:
        - cookie:
            attributes:
            - name: foo
              value: bar
            name: test
        upgradeConfigs:
        - upgradeType: websocket
