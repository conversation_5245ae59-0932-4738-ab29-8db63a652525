http:
- name: "first-listener"
  address: "::"
  port: 10080
  hostnames:
  - "*"
  path:
    mergeSlashes: true
    escapedSlashesAction: UnescapeAndRedirect
  routes:
  - name: "first-route"
    hostname: "*"
    traffic:
      loadBalancer:
        roundRobin: {}
    destination:
      name: "first-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "first-route-dest/backend/0"
  - name: "second-route"
    hostname: "*"
    traffic:
      loadBalancer:
        random: {}
    destination:
      name: "second-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "second-route-dest/backend/0"
  - name: "third-route"
    hostname: "*"
    traffic:
      loadBalancer:
        leastRequest: {}
    destination:
      name: "third-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "third-route-dest/backend/0"
  - name: "fourth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        consistentHash:
          sourceIP: true
    destination:
      name: "fourth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "fourth-route-dest/backend/0"
  - name: "fifth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        leastRequest:
          slowStart:
            window: 60s
    destination:
      name: "fifth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "fifth-route-dest/backend/0"
  - name: "sixth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        roundRobin:
          slowStart:
            window: 300s
    destination:
      name: "sixth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "sixth-route-dest/backend/0"
  - name: "seventh-route"
    hostname: "*"
    traffic:
      loadBalancer:
        consistentHash:
          header:
            name: name
    destination:
      name: "seventh-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "seventh-route-dest/backend/0"
  - name: "eighth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        consistentHash:
          tableSize: 524287
    destination:
      name: "eighth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "eighth-route-dest/backend/0"
  - name: "ninth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        consistentHash:
          cookie:
            name: "test"
    destination:
      name: "ninth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "ninth-route-dest/backend/0"
  - name: "tenth-route"
    hostname: "*"
    traffic:
      loadBalancer:
        consistentHash:
          cookie:
            name: "test"
            attributes:
              foo: bar
    destination:
      name: "tenth-route-dest"
      settings:
      - endpoints:
        - host: "*******"
          port: 50000
        name: "tenth-route-dest/backend/0"
