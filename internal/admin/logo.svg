<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120">
  <defs>
    <style>
      .logo-text {
        font-family: 'Arial', sans-serif;
        font-weight: bold;
        fill: #333;
      }
      .envoy-color {
        fill: #AC6199;
      }
      .gateway-color {
        fill: #5C4E75;
      }
    </style>
  </defs>

  <!-- Envoy Gateway Logo - Stacked Layout -->
  <g transform="translate(100, 60)">
    <!-- Simplified geometric logo mark -->
    <g transform="translate(-30, -40)">
      <!-- Main diamond/gateway shape -->
      <polygon class="envoy-color" points="30,10 50,25 30,40 10,25" />
      <!-- Inner gateway opening -->
      <polygon class="gateway-color" points="25,20 35,25 25,30 15,25" />
      <!-- Side elements representing traffic flow -->
      <rect class="envoy-color" x="5" y="23" width="8" height="4" rx="2"/>
      <rect class="envoy-color" x="47" y="23" width="8" height="4" rx="2"/>
    </g>

    <!-- Text - Stacked -->
    <text class="logo-text envoy-color" x="0" y="15" text-anchor="middle" font-size="16">ENVOY</text>
    <text class="logo-text gateway-color" x="0" y="32" text-anchor="middle" font-size="14">GATEWAY</text>
  </g>
</svg>