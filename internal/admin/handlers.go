// Copyright Envoy Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package admin

import (
	"encoding/json"
	"fmt"
	"net/http"
	"runtime"
	"runtime/metrics"
	"strings"
	"time"

	gwapiv1 "sigs.k8s.io/gateway-api/apis/v1"
	gwapiv1a2 "sigs.k8s.io/gateway-api/apis/v1alpha2"
	gwapiv1a3 "sigs.k8s.io/gateway-api/apis/v1alpha3"

	egv1a1 "github.com/envoyproxy/gateway/api/v1alpha1"
	"github.com/envoyproxy/gateway/internal/cmd/version"
	"github.com/envoyproxy/gateway/internal/envoygateway/config"
	"github.com/envoyproxy/gateway/internal/message"
)

// StatusResponse represents the overall status of Envoy Gateway
type StatusResponse struct {
	Version      string `json:"version"`
	Uptime       string `json:"uptime"`
	Status       string `json:"status"`
	ConfigStatus string `json:"configStatus"`
	Config       any    `json:"config"`
}

// ResourceInfo represents information about a Gateway resource
type ResourceInfo struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace,omitempty"`
	Status    string `json:"status"`
	Reason    string `json:"reason,omitempty"`
	Message   string `json:"message,omitempty"`
}

// ResourcesResponse represents all Gateway resources
type ResourcesResponse map[string][]ResourceInfo

// StatsResponse represents statistics information
type StatsResponse map[string]interface{}

var (
	startTime         = time.Now()
	providerResources *message.ProviderResources
)

// SetProviderResources sets the provider resources for resource queries
func SetProviderResources(resources *message.ProviderResources) {
	providerResources = resources
}

// handleWebUI serves the web UI HTML page
func handleWebUI(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html")

	// Read the embedded HTML file
	content, err := webuiFS.ReadFile("webui.html")
	if err != nil {
		http.Error(w, "Failed to load web UI", http.StatusInternalServerError)
		return
	}

	if _, err := w.Write(content); err != nil {
		http.Error(w, "Failed to write response", http.StatusInternalServerError)
		return
	}
}

// handleSVG serves SVG files
func handleSVG(w http.ResponseWriter, _ *http.Request, filename string) {
	w.Header().Set("Content-Type", "image/svg+xml")

	// Read the embedded SVG file
	content, err := webuiFS.ReadFile(filename)
	if err != nil {
		http.Error(w, "Failed to load SVG file", http.StatusInternalServerError)
		return
	}

	if _, err := w.Write(content); err != nil {
		http.Error(w, "Failed to write response", http.StatusInternalServerError)
		return
	}
}

// handleStatus returns the overall status of Envoy Gateway
func handleStatus(cfg *config.Server) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")

		uptime := time.Since(startTime).String()
		response := StatusResponse{
			Version:      version.Get().EnvoyGatewayVersion,
			Uptime:       uptime,
			Status:       "Running",
			ConfigStatus: "Loaded",
			Config:       cfg.EnvoyGateway,
		}

		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
		}
	}
}

// handleResources returns information about Gateway resources
func handleResources(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if providerResources == nil {
		// Return empty response with 200 status when provider resources are not available
		response := make(ResourcesResponse)
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
		}
		return
	}

	response := make(ResourcesResponse)

	// Get all resources from the watchable map
	allResources := providerResources.GetResources()
	if allResources == nil {
		// Return empty response if no resources
		if err := json.NewEncoder(w).Encode(response); err != nil {
			http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
		}
		return
	}

	// Process each resource type from all gateway classes
	for _, resources := range allResources {
		// Get GatewayClasses
		if resources.GatewayClass != nil {
			if response["GatewayClasses"] == nil {
				response["GatewayClasses"] = []ResourceInfo{}
			}
			info := convertGatewayClassToResourceInfo(resources.GatewayClass)
			response["GatewayClasses"] = append(response["GatewayClasses"], info)
		}

		// Get Gateways
		for _, gateway := range resources.Gateways {
			if response["Gateways"] == nil {
				response["Gateways"] = []ResourceInfo{}
			}
			info := convertGatewayToResourceInfo(gateway)
			response["Gateways"] = append(response["Gateways"], info)
		}

		// Get HTTPRoutes
		for _, httpRoute := range resources.HTTPRoutes {
			if response["HTTPRoutes"] == nil {
				response["HTTPRoutes"] = []ResourceInfo{}
			}
			info := convertHTTPRouteToResourceInfo(httpRoute)
			response["HTTPRoutes"] = append(response["HTTPRoutes"], info)
		}

		// Get GRPCRoutes
		for _, grpcRoute := range resources.GRPCRoutes {
			if response["GRPCRoutes"] == nil {
				response["GRPCRoutes"] = []ResourceInfo{}
			}
			info := convertGRPCRouteToResourceInfo(grpcRoute)
			response["GRPCRoutes"] = append(response["GRPCRoutes"], info)
		}

		// Get TCPRoutes
		for _, tcpRoute := range resources.TCPRoutes {
			if response["TCPRoutes"] == nil {
				response["TCPRoutes"] = []ResourceInfo{}
			}
			info := convertTCPRouteToResourceInfo(tcpRoute)
			response["TCPRoutes"] = append(response["TCPRoutes"], info)
		}

		// Get UDPRoutes
		for _, udpRoute := range resources.UDPRoutes {
			if response["UDPRoutes"] == nil {
				response["UDPRoutes"] = []ResourceInfo{}
			}
			info := convertUDPRouteToResourceInfo(udpRoute)
			response["UDPRoutes"] = append(response["UDPRoutes"], info)
		}

		// Get TLSRoutes
		for _, tlsRoute := range resources.TLSRoutes {
			if response["TLSRoutes"] == nil {
				response["TLSRoutes"] = []ResourceInfo{}
			}
			info := convertTLSRouteToResourceInfo(tlsRoute)
			response["TLSRoutes"] = append(response["TLSRoutes"], info)
		}

		// Get BackendTLSPolicies
		for _, backendTLSPolicy := range resources.BackendTLSPolicies {
			if response["BackendTLSPolicies"] == nil {
				response["BackendTLSPolicies"] = []ResourceInfo{}
			}
			info := convertBackendTLSPolicyToResourceInfo(backendTLSPolicy)
			response["BackendTLSPolicies"] = append(response["BackendTLSPolicies"], info)
		}

		// Get BackendTrafficPolicies
		for _, backendTrafficPolicy := range resources.BackendTrafficPolicies {
			if response["BackendTrafficPolicies"] == nil {
				response["BackendTrafficPolicies"] = []ResourceInfo{}
			}
			info := convertBackendTrafficPolicyToResourceInfo(backendTrafficPolicy)
			response["BackendTrafficPolicies"] = append(response["BackendTrafficPolicies"], info)
		}

		// Get ClientTrafficPolicies
		for _, clientTrafficPolicy := range resources.ClientTrafficPolicies {
			if response["ClientTrafficPolicies"] == nil {
				response["ClientTrafficPolicies"] = []ResourceInfo{}
			}
			info := convertClientTrafficPolicyToResourceInfo(clientTrafficPolicy)
			response["ClientTrafficPolicies"] = append(response["ClientTrafficPolicies"], info)
		}

		// Get SecurityPolicies
		for _, securityPolicy := range resources.SecurityPolicies {
			if response["SecurityPolicies"] == nil {
				response["SecurityPolicies"] = []ResourceInfo{}
			}
			info := convertSecurityPolicyToResourceInfo(securityPolicy)
			response["SecurityPolicies"] = append(response["SecurityPolicies"], info)
		}

		// Get EnvoyPatchPolicies
		for _, envoyPatchPolicy := range resources.EnvoyPatchPolicies {
			if response["EnvoyPatchPolicies"] == nil {
				response["EnvoyPatchPolicies"] = []ResourceInfo{}
			}
			info := convertEnvoyPatchPolicyToResourceInfo(envoyPatchPolicy)
			response["EnvoyPatchPolicies"] = append(response["EnvoyPatchPolicies"], info)
		}

		// Get EnvoyExtensionPolicies
		for _, envoyExtensionPolicy := range resources.EnvoyExtensionPolicies {
			if response["EnvoyExtensionPolicies"] == nil {
				response["EnvoyExtensionPolicies"] = []ResourceInfo{}
			}
			info := convertEnvoyExtensionPolicyToResourceInfo(envoyExtensionPolicy)
			response["EnvoyExtensionPolicies"] = append(response["EnvoyExtensionPolicies"], info)
		}
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
	}
}

// handleStats returns comprehensive runtime metrics using runtime/metrics package
func handleStats(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	// Get all available metrics
	descs := metrics.All()
	samples := make([]metrics.Sample, len(descs))
	for i := range samples {
		samples[i].Name = descs[i].Name
	}

	// Read all metrics
	metrics.Read(samples)

	// Organize metrics into categories
	response := StatsResponse{
		"memory":    make(map[string]interface{}),
		"gc":        make(map[string]interface{}),
		"scheduler": make(map[string]interface{}),
		"cpu":       make(map[string]interface{}),
		"system":    make(map[string]interface{}),
	}

	// Process each metric
	for _, sample := range samples {
		name, value := sample.Name, sample.Value

		switch value.Kind() {
		case metrics.KindUint64:
			addMetricToResponse(response, name, value.Uint64())
		case metrics.KindFloat64:
			addMetricToResponse(response, name, value.Float64())
		case metrics.KindFloat64Histogram:
			// For histograms, we'll include basic statistics
			hist := value.Float64Histogram()
			histStats := map[string]interface{}{
				"buckets":     len(hist.Buckets) - 1,
				"total_count": getTotalCount(hist),
			}
			addMetricToResponse(response, name, histStats)
		}
	}

	// Add basic system information
	response["system"] = map[string]interface{}{
		"uptime":     time.Since(startTime).String(),
		"cpu_count":  runtime.NumCPU(),
		"go_version": runtime.Version(),
		"gomaxprocs": runtime.GOMAXPROCS(0),
		"goroutines": runtime.NumGoroutine(),
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		http.Error(w, fmt.Sprintf("Failed to encode response: %v", err), http.StatusInternalServerError)
	}
}

// addMetricToResponse categorizes and adds a metric to the appropriate section of the response
func addMetricToResponse(response StatsResponse, name string, value interface{}) {
	// Categorize metrics based on their name prefix
	switch {
	case strings.HasPrefix(name, "/memory/"):
		response["memory"].(map[string]interface{})[name] = value
	case strings.HasPrefix(name, "/gc/"):
		response["gc"].(map[string]interface{})[name] = value
	case strings.HasPrefix(name, "/sched/"):
		response["scheduler"].(map[string]interface{})[name] = value
	case strings.HasPrefix(name, "/cpu/"):
		response["cpu"].(map[string]interface{})[name] = value
	default:
		// For other metrics, put them in a general category
		if response["other"] == nil {
			response["other"] = make(map[string]interface{})
		}
		response["other"].(map[string]interface{})[name] = value
	}
}

// getTotalCount calculates the total count from a histogram
func getTotalCount(hist *metrics.Float64Histogram) uint64 {
	var total uint64
	for _, count := range hist.Counts {
		total += count
	}
	return total
}

// Helper functions to convert resources to ResourceInfo
func convertGatewayClassToResourceInfo(gatewayClass *gwapiv1.GatewayClass) ResourceInfo {
	info := ResourceInfo{
		Name: gatewayClass.Name,
	}

	// Get the latest condition
	if len(gatewayClass.Status.Conditions) > 0 {
		condition := gatewayClass.Status.Conditions[len(gatewayClass.Status.Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertGatewayToResourceInfo(gateway *gwapiv1.Gateway) ResourceInfo {
	info := ResourceInfo{
		Name:      gateway.Name,
		Namespace: gateway.Namespace,
	}

	// Get the latest condition
	if len(gateway.Status.Conditions) > 0 {
		condition := gateway.Status.Conditions[len(gateway.Status.Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertHTTPRouteToResourceInfo(httpRoute *gwapiv1.HTTPRoute) ResourceInfo {
	info := ResourceInfo{
		Name:      httpRoute.Name,
		Namespace: httpRoute.Namespace,
	}

	// Get the latest condition from parents
	if len(httpRoute.Status.Parents) > 0 && len(httpRoute.Status.Parents[0].Conditions) > 0 {
		condition := httpRoute.Status.Parents[0].Conditions[0]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertGRPCRouteToResourceInfo(grpcRoute *gwapiv1.GRPCRoute) ResourceInfo {
	info := ResourceInfo{
		Name:      grpcRoute.Name,
		Namespace: grpcRoute.Namespace,
	}

	// Get the latest condition from parents
	if len(grpcRoute.Status.Parents) > 0 && len(grpcRoute.Status.Parents[0].Conditions) > 0 {
		condition := grpcRoute.Status.Parents[0].Conditions[0]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertTCPRouteToResourceInfo(tcpRoute *gwapiv1a2.TCPRoute) ResourceInfo {
	info := ResourceInfo{
		Name:      tcpRoute.Name,
		Namespace: tcpRoute.Namespace,
	}

	// Get the latest condition from parents
	if len(tcpRoute.Status.Parents) > 0 && len(tcpRoute.Status.Parents[0].Conditions) > 0 {
		condition := tcpRoute.Status.Parents[0].Conditions[0]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertUDPRouteToResourceInfo(udpRoute *gwapiv1a2.UDPRoute) ResourceInfo {
	info := ResourceInfo{
		Name:      udpRoute.Name,
		Namespace: udpRoute.Namespace,
	}

	// Get the latest condition from parents
	if len(udpRoute.Status.Parents) > 0 && len(udpRoute.Status.Parents[0].Conditions) > 0 {
		condition := udpRoute.Status.Parents[0].Conditions[0]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertTLSRouteToResourceInfo(tlsRoute *gwapiv1a2.TLSRoute) ResourceInfo {
	info := ResourceInfo{
		Name:      tlsRoute.Name,
		Namespace: tlsRoute.Namespace,
	}

	// Get the latest condition from parents
	if len(tlsRoute.Status.Parents) > 0 && len(tlsRoute.Status.Parents[0].Conditions) > 0 {
		condition := tlsRoute.Status.Parents[0].Conditions[0]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertBackendTLSPolicyToResourceInfo(backendTLSPolicy *gwapiv1a3.BackendTLSPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      backendTLSPolicy.Name,
		Namespace: backendTLSPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(backendTLSPolicy.Status.Ancestors) > 0 && len(backendTLSPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := backendTLSPolicy.Status.Ancestors[0].Conditions[len(backendTLSPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertBackendTrafficPolicyToResourceInfo(backendTrafficPolicy *egv1a1.BackendTrafficPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      backendTrafficPolicy.Name,
		Namespace: backendTrafficPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(backendTrafficPolicy.Status.Ancestors) > 0 && len(backendTrafficPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := backendTrafficPolicy.Status.Ancestors[0].Conditions[len(backendTrafficPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertClientTrafficPolicyToResourceInfo(clientTrafficPolicy *egv1a1.ClientTrafficPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      clientTrafficPolicy.Name,
		Namespace: clientTrafficPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(clientTrafficPolicy.Status.Ancestors) > 0 && len(clientTrafficPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := clientTrafficPolicy.Status.Ancestors[0].Conditions[len(clientTrafficPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertSecurityPolicyToResourceInfo(securityPolicy *egv1a1.SecurityPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      securityPolicy.Name,
		Namespace: securityPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(securityPolicy.Status.Ancestors) > 0 && len(securityPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := securityPolicy.Status.Ancestors[0].Conditions[len(securityPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertEnvoyPatchPolicyToResourceInfo(envoyPatchPolicy *egv1a1.EnvoyPatchPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      envoyPatchPolicy.Name,
		Namespace: envoyPatchPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(envoyPatchPolicy.Status.Ancestors) > 0 && len(envoyPatchPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := envoyPatchPolicy.Status.Ancestors[0].Conditions[len(envoyPatchPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}

func convertEnvoyExtensionPolicyToResourceInfo(envoyExtensionPolicy *egv1a1.EnvoyExtensionPolicy) ResourceInfo {
	info := ResourceInfo{
		Name:      envoyExtensionPolicy.Name,
		Namespace: envoyExtensionPolicy.Namespace,
	}

	// Get the latest condition from ancestors
	if len(envoyExtensionPolicy.Status.Ancestors) > 0 && len(envoyExtensionPolicy.Status.Ancestors[0].Conditions) > 0 {
		condition := envoyExtensionPolicy.Status.Ancestors[0].Conditions[len(envoyExtensionPolicy.Status.Ancestors[0].Conditions)-1]
		info.Status = string(condition.Status)
		info.Reason = condition.Reason
		info.Message = condition.Message
	}

	return info
}
